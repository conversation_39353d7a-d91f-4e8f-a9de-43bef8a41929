/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  experimental: {
    // Remove any Turbopack-specific experimental configurations
  },
  env: {
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'https://cleaningservicesmp.info',
    SKIP_SITEMAP_GENERATION: process.env.SKIP_SITEMAP_GENERATION || 'true',
    FORCE_SITEMAP_GENERATION: process.env.FORCE_SITEMAP_GENERATION || 'false',
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        net: false,
        tls: false,
        fs: false,
        dns: false,
        child_process: false,
        'fs/promises': false
      };
    }
    return config;
  },
  generateBuildId: async () => {
    return 'build-' + Date.now();
  },
  generateEtags: true,
  poweredByHeader: false,
  trailingSlash: false,
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
};

export default nextConfig; 