import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import { Cache } from '@/app/lib/cache';

// Remove force-dynamic to allow caching
// export const dynamic = 'force-dynamic';
export const runtime = 'nodejs';

// Initialize cache once at module level with shorter TTL for search results
const cache = new Cache({ 
  collection: 'searchResults', 
  debug: true,
  ttl: 604800 // 1 week cache for search results instead of 1 year
});

// Define the shape of a Google Places API result
interface GooglePlaceResult {
  name: string;
  formatted_address: string;
  rating?: number;
  types: string[];
  place_id: string;
  geometry?: {
    location: {
      lat: number;
      lng: number;
    };
  };
  formatted_phone_number?: string;
  website?: string;
}

// Normalize query to ensure consistent cache keys
function normalizeQuery(query: string): string {
  // Clean and standardize the query
  let normalizedQuery = query.toLowerCase().trim();
  
  // Split into service and location parts
  const parts = normalizedQuery.split(/\s+(?:in|at|\+)\s+/);
  
  if (parts.length < 2) {
    return normalizedQuery;
  }

  // Get location (last part) and service (everything else)
  const location = parts[parts.length - 1].replace(/\s*madhya\s*pradesh\s*/gi, '').trim();
  const service = parts.slice(0, -1).join(' ').trim();
  
  // Ensure "cleaning services" is included if not present
  const normalizedService = service.includes('cleaning') ? service : `${service} cleaning services`;
  
  // Construct final query
  return `${normalizedService} in ${location}`;
}

export async function GET(request: Request) {
  // Generate a unique request ID
  const requestId = Date.now().toString();
  console.log(`\n[Search:${requestId}] ========== New Search Request ==========`);

  try {
    const url = new URL(request.url);
    const { searchParams } = url;
    const query = searchParams.get('q');
    const bypassCache = searchParams.get('bypass_cache') === 'true';
    const retryAttempt = parseInt(searchParams.get('retry_attempt') || '0');

    if (!query) {
      throw new Error('No query provided');
    }

    console.log(`[Search:${requestId}] Original query:`, query);
    const normalizedQuery = normalizeQuery(query);
    console.log(`[Search:${requestId}] Normalized query:`, normalizedQuery);

    // Try to get from cache first unless bypass_cache is true
    if (!bypassCache) {
      const cacheKey = `search:${normalizedQuery}`;
      try {
        const cachedResults = await cache.get(cacheKey);
        if (cachedResults && Array.isArray(cachedResults) && cachedResults.length > 0) {
          console.log(`[Search:${requestId}] Cache HIT for query:`, normalizedQuery);
          return new NextResponse(JSON.stringify(cachedResults), {
            status: 200,
            headers: {
              'Content-Type': 'application/json',
              'Cache-Control': 'public, max-age=604800',
              'X-Cache': 'HIT',
              'X-Cache-Key': cacheKey,
              'X-Cache-Debug': 'true',
              'X-Request-ID': requestId
            }
          });
        }
      } catch (cacheError) {
        console.error(`[Search:${requestId}] Cache error:`, cacheError);
        // Continue with API call on cache error
      }
    }

    // Fetch from Google Places API with retries
    const apiKey = process.env.GOOGLE_PLACES_API_KEY;
    if (!apiKey) {
      throw new Error('Google Places API key is not configured');
    }

    const apiUrl = new URL('https://maps.googleapis.com/maps/api/place/textsearch/json');
    apiUrl.searchParams.set('query', `${normalizedQuery}, madhya pradesh, india`);
    apiUrl.searchParams.set('key', apiKey);
    apiUrl.searchParams.set('region', 'in');
    apiUrl.searchParams.set('type', 'cleaning_service|home_service');

    console.log(`[Search:${requestId}] API URL:`, apiUrl.toString().replace(apiKey, 'REDACTED'));

    const controller = new AbortController();
    const timeout = setTimeout(() => controller.abort(), 30000);

    try {
      const apiResponse = await fetch(apiUrl.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'CleaningServicesMP/1.0'
        },
        signal: controller.signal
      });

      clearTimeout(timeout);

      if (!apiResponse.ok) {
        throw new Error(`Google Places API returned status: ${apiResponse.status}`);
      }

      const data = await apiResponse.json();
      
      if (!data.results || !Array.isArray(data.results)) {
        throw new Error('Invalid API response format');
      }

      // If no results and first attempt, try with a modified query
      if (data.results.length === 0 && retryAttempt === 0) {
        const retryUrl = new URL(request.url);
        retryUrl.searchParams.set('retry_attempt', '1');
        retryUrl.searchParams.set('bypass_cache', 'true');
        
        // Retry with a more generic query
        const genericQuery = normalizedQuery.replace(/cleaning\s+services?/gi, 'cleaning');
        retryUrl.searchParams.set('q', genericQuery);
        
        return fetch(retryUrl.toString(), {
          method: 'GET',
          headers: request.headers
        });
      }

      if (data.results.length === 0) {
        return new NextResponse(JSON.stringify([]), {
          status: 200,
          headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-store',
            'X-Request-ID': requestId
          }
        });
      }

      // Process results with improved error handling
      const detailedResults = await Promise.all(
        data.results.map(async (result: GooglePlaceResult) => {
          try {
            const details = await fetchPlaceDetails(result.place_id, apiKey);
            return {
              name: result.name,
              address: result.formatted_address,
              rating: result.rating,
              types: result.types,
              place_id: result.place_id,
              location: result.geometry?.location,
              phone_number: details.formatted_phone_number,
              website: details.website,
              business_url: details.url || `https://www.google.com/maps/place/?q=place_id:${result.place_id}`
            };
          } catch (error) {
            console.error(`[Search:${requestId}] Error fetching details for ${result.place_id}:`, error);
            // Return basic result if details fetch fails
            return {
              name: result.name,
              address: result.formatted_address,
              rating: result.rating,
              types: result.types,
              place_id: result.place_id,
              location: result.geometry?.location,
              business_url: `https://www.google.com/maps/place/?q=place_id:${result.place_id}`
            };
          }
        })
      );

      // Cache successful results
      if (!bypassCache && detailedResults.length > 0) {
        const cacheKey = `search:${normalizedQuery}`;
        await cache.set(cacheKey, detailedResults).catch(error => {
          console.error(`[Search:${requestId}] Error caching results:`, error);
        });
      }

      return new NextResponse(JSON.stringify(detailedResults), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'public, max-age=604800',
          'X-Cache': 'MISS',
          'X-Request-ID': requestId
        }
      });

    } catch (error) {
      clearTimeout(timeout);
      throw error;
    }

  } catch (error) {
    console.error(`[Search:${requestId}] Error processing search:`, error);
    return new NextResponse(
      JSON.stringify({ 
        error: error instanceof Error ? error.message : 'An unexpected error occurred',
        requestId 
      }), 
      {
        status: error instanceof Error && error.message === 'No query provided' ? 400 : 500,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-store'
        }
      }
    );
  }
}

// Helper function to fetch place details
async function fetchPlaceDetails(placeId: string, apiKey: string) {
  const detailsController = new AbortController();
  const detailsTimeout = setTimeout(() => detailsController.abort(), 10000);

  try {
    const detailsUrl = new URL('https://maps.googleapis.com/maps/api/place/details/json');
    detailsUrl.searchParams.set('place_id', placeId);
    detailsUrl.searchParams.set('key', apiKey);
    detailsUrl.searchParams.set('fields', 'formatted_phone_number,website,url');

    const detailsResponse = await fetch(detailsUrl.toString(), {
      signal: detailsController.signal
    });

    clearTimeout(detailsTimeout);

    if (!detailsResponse.ok) {
      throw new Error(`Details API returned status: ${detailsResponse.status}`);
    }

    const detailsData = await detailsResponse.json();
    return detailsData.result || {};
  } catch (error) {
    clearTimeout(detailsTimeout);
    throw error;
  }
} 