import React, { Suspense } from 'react';
import Link from 'next/link';
import { SearchBar } from './components/SearchBar';
import { ContactForm } from './components/ContactForm';
import { Location, Keyword } from './types';
import { Cache } from './lib/cache';
import { Metadata } from 'next';
import { NativeBanner } from './components/ads/NativeBanner';
import { MontagNativeBanner } from './components/ads/MontagNativeBanner';
import { AdsterraBanner } from './components/ads/AdsterraBanner';

export const metadata: Metadata = {
  alternates: {
    canonical: 'https://cleaningservicesmp.info'
  }
};

async function getLocationsAndServices(): Promise<{ locations: Location[]; services: Keyword[] }> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://cleaningservicesmp.info';

    const cache = new Cache({ 
      debug: true 
    });
    const cacheKey = 'home:locationsAndServices';
    
    // Check cache first
    const cachedData = await cache.get<{ locations: Location[]; services: Keyword[] }>(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Fetch if not in cache
    const [locationsRes, servicesRes] = await Promise.all([
      fetch(`${baseUrl}/api/locations`),
      fetch(`${baseUrl}/api/keywords`)
    ]);

    if (!locationsRes.ok || !servicesRes.ok) {
      console.error('Failed to fetch data:', {
        locationsStatus: locationsRes.status,
        servicesStatus: servicesRes.status
      });
      return { locations: [], services: [] };
    }

    const [locations, services] = await Promise.all([
      locationsRes.json(),
      servicesRes.json()
    ]);

    const data = { locations, services };
    
    // Cache for 6 months (default duration)
    await cache.set(cacheKey, data);

    return data;
  } catch (error) {
    console.error('Error fetching data:', error);
    return { locations: [], services: [] };
  }
}

export default async function Home(): Promise<React.JSX.Element> {
  const { locations, services } = await getLocationsAndServices();

  return (
    <main>
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-purple-600 via-blue-600 to-purple-700 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Find Top-Rated Cleaning Services in Madhya Pradesh
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-purple-100">
              Professional, reliable, and trusted cleaning services for your home and business
            </p>
            <div className="max-w-4xl mx-auto">
              <SearchBar />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Services */}
      <section className="py-16 bg-gradient-to-b from-purple-50 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Our Services
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => (
              <Link
                key={service.slug || service.keyword}
                href={`/locations?service=${encodeURIComponent(service.keyword)}`}
                className="block transform transition-all duration-300 hover:scale-105"
              >
                <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow h-full border border-purple-100">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    {service.keyword}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Professional {service.keyword.toLowerCase()} services in Madhya Pradesh.
                    Book now for quality service at competitive prices.
                  </p>
                  <div className="flex items-center text-purple-600 font-semibold group">
                    Choose Location
                    <svg 
                      className="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* Ad Units */}
          <NativeBanner />
          <MontagNativeBanner />
          <AdsterraBanner />

          <div className="text-center mt-8">
            <Link
              href="/services"
              className="inline-flex items-center text-purple-600 font-semibold hover:text-purple-800 transition-colors"
            >
              View All Services
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </section>

      {/* Contact Form Section */}
      <section className="py-16">
        <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Get a Free Quote
          </h2>
          <div className="bg-white p-8 rounded-lg shadow-md">
            <Suspense fallback={<div>Loading form...</div>}>
              <ContactForm locations={locations} services={services} />
            </Suspense>
          </div>
        </div>
      </section>

      {/* Cities Section */}
      <section className="py-16 bg-gradient-to-b from-white to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Service Locations
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {locations.map((location) => (
              <Link
                key={location.slug || location.Places}
                href={`/services/${location.slug || location.Places.toLowerCase().replace(/\s+/g, '-')}`}
                className="block transform transition-all duration-300 hover:scale-105"
              >
                <div className="bg-white p-4 rounded-lg shadow-sm hover:shadow-md transition-shadow text-center h-full border border-purple-100">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {location.Places}
                  </h3>
                  <p className="text-sm text-gray-600 mb-3">{location.District}</p>
                  <div className="text-purple-600 font-semibold text-sm group">
                    View Services
                    <svg 
                      className="w-4 h-4 ml-1 inline-block transform group-hover:translate-x-1 transition-transform" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </Link>
            ))}
          </div>
          <div className="text-center mt-8">
            <Link
              href="/locations"
              className="inline-flex items-center text-purple-600 font-semibold hover:text-purple-800 transition-colors"
            >
              View All Locations
              <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>
      </section>
    </main>
  );
}
