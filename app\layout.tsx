import React from 'react';
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "./components/Header";
import { Footer } from "./components/Footer";
import { MontagScripts } from "./components/ads/MontagScripts";
import { MontagInterstitial } from "./components/ads/MontagInterstitial";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  metadataBase: new URL('https://cleaningservicesmp.info'),
  title: {
    default: "Cleaning Services MP - Professional Cleaning Services in Madhya Pradesh",
    template: "%s | Cleaning Services MP"
  },
  description: "Find top-rated cleaning services in Madhya Pradesh. Professional residential and commercial cleaning services for your home and business.",
  keywords: ["cleaning services", "madhya pradesh", "home cleaning", "commercial cleaning", "professional cleaners", "cleaning services mp", "mp cleaning"],
  icons: {
    icon: '/favicon-new.ico',
    shortcut: '/favicon-new.ico',
    apple: '/favicon-new.ico',
    other: {
      rel: 'apple-touch-icon-precomposed',
      url: '/favicon-new.ico',
    },
  },
  verification: {
    google: 'google-site-verification-code',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  }
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({
  children,
}: RootLayoutProps): React.JSX.Element {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
        {(process.env.NEXT_PUBLIC_AD_NETWORK === 'monetag' || process.env.NEXT_PUBLIC_AD_NETWORK === 'both') && (
          <meta name="monetag" content={process.env.NEXT_PUBLIC_MONETAG_VERIFICATION} />
        )}
        <MontagScripts />
        <MontagInterstitial />
      </head>
      <body className={inter.className}>
        <div className="flex flex-col min-h-screen">
          <Header />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}