interface SearchResult {
  name: string;
  address: string;
  rating?: number;
  types: string[];
  place_id: string;
  location?: {
    lat: number;
    lng: number;
  };
  phone_number?: string;
  website?: string;
  business_url: string;
}

interface SearchResultsProps {
  query?: string;
  results?: SearchResult[];
}

export function SearchResults({ query, results = [] }: SearchResultsProps) {
  if (!query) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">Please enter a search term</p>
      </div>
    );
  }

  if (!results || results.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No results found for &quot;{query}&quot;</p>
        <p className="text-sm text-gray-500 mt-2">Please try a different search term</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="grid gap-6">
        {results.map((result) => (
          <div
            key={result.place_id}
            className="bg-white p-6 rounded-lg shadow-md hover:shadow-xl transition-shadow border border-gray-200"
          >
            <div className="flex justify-between items-start mb-4">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {result.name}
                </h2>
                <p className="text-gray-600 mt-1">{result.address}</p>
                {result.phone_number && (
                  <p className="text-gray-600 mt-1">
                    <a href={`tel:${result.phone_number}`} className="hover:text-blue-600">
                      {result.phone_number}
                    </a>
                  </p>
                )}
              </div>
              {result.rating && (
                <div className="flex items-center px-3 py-1 bg-yellow-100 rounded-full">
                  <span className="text-yellow-500 mr-1">★</span>
                  <span className="text-gray-700 font-medium">{result.rating.toFixed(1)}</span>
                </div>
              )}
            </div>

            <div className="flex flex-wrap gap-2 mt-4">
              {result.types.map((type) => (
                <span
                  key={type}
                  className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                >
                  {type.replace(/_/g, ' ')}
                </span>
              ))}
            </div>

            <div className="flex gap-4 mt-4">
              {result.website && (
                <a
                  href={result.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-600 hover:text-blue-800"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                  </svg>
                  Website
                </a>
              )}
              <a
                href={result.business_url}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-blue-600 hover:text-blue-800"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" />
                </svg>
                Business Page
              </a>
            </div>

            {result.location && (
              <a
                href={`https://www.google.com/maps/search/?api=1&query=${result.location.lat},${result.location.lng}`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-blue-600 hover:text-blue-800 mt-4"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                View on Map
              </a>
            )}
          </div>
        ))}
      </div>
    </div>
  );
} 