'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Location, Keyword } from '../types';

export const SearchBar = () => {
  const router = useRouter();
  const [locations, setLocations] = useState<Location[]>([]);
  const [keywords, setKeywords] = useState<Keyword[]>([]);
  const [selectedLocation, setSelectedLocation] = useState('');
  const [selectedKeyword, setSelectedKeyword] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Fetch data with cache-control headers
        const [locationsRes, keywordsRes] = await Promise.all([
          fetch('/api/locations', {
            method: 'GET',
            cache: 'force-cache'
          }),
          fetch('/api/keywords', {
            method: 'GET',
            cache: 'force-cache'
          })
        ]);

        if (!locationsRes.ok) {
          const errorText = await locationsRes.text();
          console.error('Locations response:', errorText);
          throw new Error(`Failed to fetch locations: ${locationsRes.status} - ${errorText}`);
        }
        if (!keywordsRes.ok) {
          const errorText = await keywordsRes.text();
          console.error('Keywords response:', errorText);
          throw new Error(`Failed to fetch keywords: ${keywordsRes.status} - ${errorText}`);
        }

        const locationsData = await locationsRes.json();
        const keywordsData = await keywordsRes.json();

        console.log('Raw locations data:', locationsData);
        console.log('Raw keywords data:', keywordsData);

        if (!Array.isArray(locationsData)) {
          console.error('Invalid locations data structure:', locationsData);
          throw new Error('Invalid locations data received - not an array');
        }
        if (!Array.isArray(keywordsData)) {
          console.error('Invalid keywords data structure:', keywordsData);
          throw new Error('Invalid keywords data received - not an array');
        }

        if (locationsData.length === 0) {
          console.warn('Locations array is empty');
        }
        if (keywordsData.length === 0) {
          console.warn('Keywords array is empty');
        }

        // Sort locations alphabetically
        const sortedLocations = [...locationsData].sort((a, b) => 
          a.Places.localeCompare(b.Places)
        );

        // Sort keywords alphabetically
        const sortedKeywords = [...keywordsData].sort((a, b) => 
          a.keyword.localeCompare(b.keyword)
        );

        console.log('Processed locations:', sortedLocations.length, 'items');
        console.log('Processed keywords:', sortedKeywords.length, 'items');

        setLocations(sortedLocations);
        setKeywords(sortedKeywords);
      } catch (error) {
        console.error('Error in SearchBar:', error);
        setError(error instanceof Error ? error.message : 'An unexpected error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedLocation && selectedKeyword) {
      const searchQuery = `${selectedKeyword} in ${selectedLocation} Madhya Pradesh`;
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  if (error) {
    return (
      <div className="text-center p-4 bg-red-50 rounded-lg">
        <p className="text-red-600">Error loading search options. Please try again later.</p>
        <p className="text-sm text-red-500 mt-1">{error}</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-[1fr,1fr,auto] gap-4">
        <div className="h-[72px] bg-blue-400/30 rounded-lg animate-pulse"></div>
        <div className="h-[72px] bg-blue-400/30 rounded-lg animate-pulse"></div>
        <div className="h-[72px] w-[120px] bg-blue-400/30 rounded-lg animate-pulse"></div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSearch} className="grid grid-cols-1 md:grid-cols-[1fr,1fr,auto] gap-4 bg-white/10 backdrop-blur-md p-4 rounded-xl shadow-lg">
      <div className="flex flex-col">
        <label htmlFor="location" className="text-sm font-medium text-white mb-1 ml-1">
          Select Location
        </label>
        <select
          id="location"
          value={selectedLocation}
          onChange={(e) => setSelectedLocation(e.target.value)}
          className="w-full h-[48px] px-4 bg-white text-gray-900 rounded-lg border-2 border-transparent focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:outline-none transition-colors text-base font-medium shadow-sm"
          required
        >
          <option value="">Choose a location</option>
          {locations.map((location) => (
            <option key={location.slug || location.Places} value={location.Places}>
              {location.Places}
            </option>
          ))}
        </select>
      </div>

      <div className="flex flex-col">
        <label htmlFor="service" className="text-sm font-medium text-white mb-1 ml-1">
          Select Service
        </label>
        <select
          id="service"
          value={selectedKeyword}
          onChange={(e) => setSelectedKeyword(e.target.value)}
          className="w-full h-[48px] px-4 bg-white text-gray-900 rounded-lg border-2 border-transparent focus:border-blue-400 focus:ring-2 focus:ring-blue-400 focus:outline-none transition-colors text-base font-medium shadow-sm"
          required
        >
          <option value="">Choose a service</option>
          {keywords.map((kw) => (
            <option key={kw.slug || kw.keyword} value={kw.keyword}>
              {kw.keyword}
            </option>
          ))}
        </select>
      </div>

      <div className="flex items-end">
        <button
          type="submit"
          className="w-full md:w-auto h-[48px] px-8 bg-white text-blue-600 rounded-lg hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-blue-600 font-semibold text-base transition-colors shadow-sm"
        >
          Search
        </button>
      </div>
    </form>
  );
}; 