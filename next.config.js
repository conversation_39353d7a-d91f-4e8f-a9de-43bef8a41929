/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        'mongodb-client-encryption': false,
        'aws4': false,
        'kerberos': false,
        '@mongodb-js/zstd': false,
        '@aws-sdk/credential-providers': false,
        'gcp-metadata': false,
        'snappy': false,
        'socks': false,
      };
    }
    return config;
  },
  serverExternalPackages: ['mongodb'],
  env: {
    NEXT_PUBLIC_SITE_URL: 'https://cleaningservicesmp.info',
    SKIP_SITEMAP_GENERATION: 'true',
    FORCE_SITEMAP_GENERATION: 'false',
    NEXT_PRIVATE_DEBUG_CACHE: '1' // Enable cache debugging
  },
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  eslint: {
    // Disabling ESLint during builds for deployment
    ignoreDuringBuilds: true,
  },
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://staupsoaksy.net https://*.revenuecpmgate.com https://al5sm.com https://groleegni.net https://*.highperformanceformat.com https://my.rtmark.net https://grubsaithapsa.net https://professionaltrafficmonitor.com https://rashcolonizeexpand.com https://submergepremierehibernate.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.googleapis.com https://*.google.com https://staupsoaksy.net https://*.revenuecpmgate.com https://al5sm.com https://groleegni.net https://*.highperformanceformat.com https://my.rtmark.net https://grubsaithapsa.net https://professionaltrafficmonitor.com https://rashcolonizeexpand.com https://submergepremierehibernate.com; frame-src 'self' https://*.google.com https://rashcolonizeexpand.com;"
          }
        ]
      }
    ];
  },
  images: {
    domains: ['maps.googleapis.com', 'maps.gstatic.com'],
  }
}

module.exports = nextConfig 