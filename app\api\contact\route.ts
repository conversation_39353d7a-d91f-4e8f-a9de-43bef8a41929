import { NextResponse } from 'next/server';
import clientPromise from '@/app/lib/mongodb';
import { ContactForm } from '@/app/types';

export async function POST(request: Request) {
  try {
    const data: ContactForm = await request.json();
    const client = await clientPromise;
    const db = client.db('cleaningservices');
    
    // Add timestamp
    const formData = {
      ...data,
      timestamp: new Date(),
    };

    const result = await db.collection('inquiries').insertOne(formData);

    return NextResponse.json({ 
      success: true, 
      message: 'Inquiry submitted successfully',
      id: result.insertedId 
    });
  } catch (error) {
    console.error('Error submitting inquiry:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to submit inquiry' },
      { status: 500 }
    );
  }
} 