'use client';

export function AdsterraVerification() {
  const network = process.env.NEXT_PUBLIC_AD_NETWORK;
  
  // Show Adsterra verification if network is 'adsterra' OR 'both'
  if ((network === 'adsterra' || network === 'both') && process.env.NEXT_PUBLIC_ADSTERRA_VERIFICATION) {
    return (
      <meta name="adsterra-site-verification" content={process.env.NEXT_PUBLIC_ADSTERRA_VERIFICATION} />
    );
  }
  
  return null;
}
