'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { SearchResults } from '@/app/components/SearchResults';
import { SearchBar } from '@/app/components/SearchBar';

interface SearchResult {
  name: string;
  address: string;
  rating?: number;
  types: string[];
  place_id: string;
  location?: {
    lat: number;
    lng: number;
  };
  phone_number?: string;
  website?: string;
  business_url: string;
}

type SearchPageProps = {
  searchParams: { q?: string }
};

export default function SearchPage({ searchParams }: SearchPageProps) {
  const [query, setQuery] = useState<string | undefined>(searchParams.q);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const abortControllerRef = useRef<AbortController>();

  const fetchResults = useCallback(async (bypassCache: boolean = false) => {
    if (!searchParams.q) return;

    // Clear previous error
    setError(null);

    // Cancel previous request if any
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController();

    setIsLoading(true);

    try {
      const searchUrl = new URL('/api/search', window.location.origin);
      searchUrl.searchParams.set('q', searchParams.q);
      if (bypassCache) {
        searchUrl.searchParams.set('bypass_cache', 'true');
      }

      console.log('Fetching results from:', searchUrl.toString());

      const response = await fetch(searchUrl.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        },
        signal: abortControllerRef.current.signal,
        cache: 'no-store'
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        try {
          const errorData = JSON.parse(errorText);
          throw new Error(errorData.error || `Search failed with status: ${response.status}`);
        } catch (parseError) {
          throw new Error(`Search failed with status: ${response.status}. Response: ${errorText}`);
        }
      }

      let data;
      const responseText = await response.text();
      
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Failed to parse response:', responseText);
        throw new Error('Invalid JSON response from server');
      }

      if (data.error) {
        throw new Error(data.error);
      }

      // Validate response structure
      if (!Array.isArray(data)) {
        throw new Error('Invalid response format: expected an array of results');
      }

      setResults(data);

      // If no results and haven't retried yet, try without cache
      if (data.length === 0 && !bypassCache && retryCount < 1) {
        console.log('No results found, retrying with fresh API call...');
        setRetryCount(prev => prev + 1);
        await fetchResults(true);
      }

    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        // Ignore abort errors
        return;
      }
      
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      console.error('Search error:', errorMessage);
      setError(errorMessage);
      setResults([]);
      
      // If error occurs and haven't retried, try again with bypass cache
      if (!bypassCache && retryCount < 1) {
        console.log('Error occurred, retrying with fresh API call...');
        setRetryCount(prev => prev + 1);
        await fetchResults(true);
      }
    } finally {
      setIsLoading(false);
    }
  }, [searchParams.q, retryCount]);

  // Reset retry count when query changes
  useEffect(() => {
    setRetryCount(0);
  }, [searchParams.q]);

  // Cleanup abort controller
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Initial fetch
  useEffect(() => {
    if (searchParams.q) {
      fetchResults();
    }
  }, [fetchResults, searchParams.q]);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <SearchBar />
      </div>

      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
          <p className="text-red-700">{error}</p>
          <button
            onClick={() => fetchResults(true)}
            className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
          >
            Try again
          </button>
        </div>
      )}

      {!isLoading && !error && (
        <SearchResults query={searchParams.q} results={results} />
      )}
    </div>
  );
}

export const dynamic = 'force-dynamic'; 