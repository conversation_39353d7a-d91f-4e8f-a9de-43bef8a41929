'use client';

import { useRouter } from 'next/navigation';

interface ClientButtonProps {
  href: string;
  className?: string;
  children: React.ReactNode;
}

export const ClientButton = ({ href, className = '', children }: ClientButtonProps) => {
  const router = useRouter();

  const handleClick = () => {
    router.push(href);
  };

  return (
    <button
      onClick={handleClick}
      className={`${className} transition-all duration-300`}
    >
      {children}
    </button>
  );
}; 