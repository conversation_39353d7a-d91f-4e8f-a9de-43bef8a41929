import React from 'react';
import { Metadata } from 'next';
import { ContactForm } from '@/app/components/ContactForm';
import { Location, Keyword } from '@/app/types';
import { Cache } from '@/app/lib/cache';

export const metadata: Metadata = {
  title: 'Contact Us - Cleaning Services MP',
  description: 'Get in touch with us for professional cleaning services across Madhya Pradesh.',
};

async function getLocationsAndServices(): Promise<{ locations: Location[]; services: Keyword[] }> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 
                    process.env.VERCEL_URL || 
                    'http://localhost:3000';

    const cache = new Cache();
    const cacheKey = 'contact:locationsAndServices';
    
    // Check cache first
    const cachedData = await cache.get<{ locations: Location[]; services: Keyword[] }>(cacheKey);
    if (cachedData) {
      return cachedData;
    }

    // Fetch if not in cache
    const [locationsRes, servicesRes] = await Promise.all([
      fetch(`${baseUrl}/api/locations`),
      fetch(`${baseUrl}/api/keywords`)
    ]);

    if (!locationsRes.ok || !servicesRes.ok) {
      console.error('Failed to fetch data:', {
        locationsStatus: locationsRes.status,
        servicesStatus: servicesRes.status
      });
      return { locations: [], services: [] };
    }

    const [locations, services] = await Promise.all([
      locationsRes.json(),
      servicesRes.json()
    ]);

    const data = { locations, services };
    
    // Cache for 6 months (default duration)
    await cache.set(cacheKey, data);

    return data;
  } catch (error) {
    console.error('Error fetching data:', error);
    return { locations: [], services: [] };
  }
}

export default async function ContactPage(): Promise<React.JSX.Element> {
  const { locations, services } = await getLocationsAndServices();

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-12">
      <div className="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-gray-900 mb-4">
            Contact Us
          </h1>
          <p className="text-xl text-gray-600">
            Get in touch with us for professional cleaning services
          </p>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8">
          <ContactForm locations={locations} services={services} />
        </div>
      </div>
    </div>
  );
} 