import React from 'react';
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Header } from "./components/Header";
import { Footer } from "./components/Footer";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  metadataBase: new URL('https://cleaningservicesmp.info'),
  title: {
    default: "Cleaning Services MP - Professional Cleaning Services in Madhya Pradesh",
    template: "%s | Cleaning Services MP"
  },
  description: "Find top-rated cleaning services in Madhya Pradesh. Professional residential and commercial cleaning services for your home and business.",
  keywords: ["cleaning services", "madhya pradesh", "home cleaning", "commercial cleaning", "professional cleaners", "cleaning services mp", "mp cleaning"],
  icons: {
    icon: '/favicon-new.ico',
    shortcut: '/favicon-new.ico',
    apple: '/favicon-new.ico',
    other: {
      rel: 'apple-touch-icon-precomposed',
      url: '/favicon-new.ico',
    },
  },
  verification: {
    google: 'google-site-verification-code',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
    },
  }
};

interface RootLayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({
  children,
}: RootLayoutProps): React.JSX.Element {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
        <meta name="monetag" content="0d3eed2ccd641657ce614f6144030224" />
        <script type='text/javascript' src='//pl27658604.revenuecpmgate.com/11/64/28/116428eac8d97c8f791907240a92c277.js'></script>
        <script src="https://staupsoaksy.net/act/files/tag.min.js?z=9863708" data-cfasync="false" async></script>
      </head>
      <body className={inter.className}>
        <div className="flex flex-col min-h-screen">
          <Header />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}