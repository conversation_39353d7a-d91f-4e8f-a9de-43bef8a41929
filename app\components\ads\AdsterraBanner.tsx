'use client';

export function AdsterraBanner() {
  const network = process.env.NEXT_PUBLIC_AD_NETWORK;
  
  // Show Adsterra banner if network is 'adsterra' OR 'both'
  if (network === 'adsterra' || network === 'both') {
    return (
      <>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              atOptions = {
                'key' : '141679e0bcf8bbb515cf7e1157541cd8',
                'format' : 'iframe',
                'height' : 60,
                'width' : 468,
                'params' : {}
              };
            `
          }}
        />
        <script 
          type="text/javascript" 
          src="//www.highperformanceformat.com/141679e0bcf8bbb515cf7e1157541cd8/invoke.js"
        />
      </>
    );
  }
  
  return null;
}
