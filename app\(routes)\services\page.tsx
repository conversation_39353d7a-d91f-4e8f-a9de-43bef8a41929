import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import { Keyword } from '@/app/types';
import { Cache } from '@/app/lib/cache';

export const metadata: Metadata = {
  title: 'Cleaning Services - Find Professional Cleaners in MP',
  description: 'Explore our comprehensive range of cleaning services available across Madhya Pradesh. Professional and reliable cleaning solutions.',
  alternates: {
    canonical: 'https://cleaningservicesmp.info/services'
  }
};

async function getServices(): Promise<Keyword[]> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://cleaningservicesmp.info';

    const cache = new Cache({
      debug: true
    });
    const cacheKey = 'services:allKeywords';
    
    // Check cache first
    const cachedServices = await cache.get<Keyword[]>(cacheKey);
    if (cachedServices) {
      return cachedServices;
    }

    // Fetch if not in cache
    const response = await fetch(`${baseUrl}/api/keywords`);

    if (!response.ok) {
      console.error('Failed to fetch services:', response.status);
      return [];
    }

    const services = await response.json();
    
    // Cache for 6 months (default duration)
    await cache.set(cacheKey, services);

    return services;
  } catch (error) {
    console.error('Error fetching services:', error);
    return [];
  }
}

export default async function ServicesPage() {
  const services = await getServices();

  if (!services.length) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900">No services found</h1>
            <p className="mt-4 text-lg text-gray-600">
              Please try again later.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-gray-900 mb-4">
            Our Cleaning Services
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Professional cleaning services tailored to your needs
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {services.map((service) => (
            <Link
              key={service.slug || service.keyword}
              href={`/locations?service=${encodeURIComponent(service.keyword)}`}
              className="block transform transition-all duration-300 hover:scale-105"
            >
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl border border-gray-100 h-full">
                <div className="p-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-4">
                    {service.keyword}
                  </h2>
                  <p className="text-gray-600 mb-4">
                    Professional {service.keyword.toLowerCase()} services across MP.
                    Choose your location to find service providers near you.
                  </p>
                  <div className="inline-flex items-center text-blue-600 font-semibold group">
                    Choose Location
                    <svg 
                      className="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
} 