'use client';

export function MontagNativeBanner() {
  const network = process.env.NEXT_PUBLIC_AD_NETWORK;
  
  // Show Monetag native banner if network is 'monetag' OR 'both'
  if ((network === 'monetag' || network === 'both') && process.env.NEXT_PUBLIC_MONETAG_NATIVE_ZONE) {
    return (
      <div 
        id={`monetag-native-${process.env.NEXT_PUBLIC_MONETAG_NATIVE_ZONE}`}
        className="monetag-native-banner"
        style={{ minHeight: '100px', margin: '20px 0', textAlign: 'center' }}
      />
    );
  }
  
  return null;
}
