'use client';

import React, { useEffect } from 'react';

interface ErrorPageProps {
  error: Error & { digest?: string };
  reset: () => void;
}

export default function ErrorPage({
  error,
  reset,
}: ErrorPageProps): React.JSX.Element {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error);
  }, [error]);

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Something went wrong
          </h1>
          <p className="text-lg text-gray-600 mb-8">
            {error.message || 'An unexpected error occurred'}
            {error.digest && (
              <span className="block text-sm text-gray-500 mt-2">
                Error ID: {error.digest}
              </span>
            )}
          </p>
          <div className="space-y-4">
            <button
              onClick={() => reset()}
              className="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors"
            >
              Try again
            </button>
            <button
              onClick={() => window.location.href = '/'}
              className="block mx-auto text-purple-600 hover:text-purple-800 transition-colors"
            >
              Return to home page
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 