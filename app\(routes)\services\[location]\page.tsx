import React from 'react';
import { Metadata } from 'next';
import { Location, Keyword } from '@/app/types';
import { SearchBar } from '@/app/components/SearchBar';
import { Cache } from '@/app/lib/cache';

interface ServicesPageProps {
  params: Promise<Record<string, string>>;
  searchParams: Promise<Record<string, string>>;
}

async function getLocationData(slug: string): Promise<Location | null> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://cleaningservicesmp.info';

    const cache = new Cache({
      debug: true
    });
    const cacheKey = 'locations:allLocations';
    
    // Check cache first
    const cachedLocations = await cache.get<Location[]>(cacheKey);
    if (cachedLocations) {
      return cachedLocations.find(loc => loc.slug === slug || loc.Places.toLowerCase().replace(/\s+/g, '-') === slug) || null;
    }

    // Fetch if not in cache
    const response = await fetch(`${baseUrl}/api/locations`);

    if (!response.ok) {
      throw new Error('Failed to fetch locations');
    }

    const locations: Location[] = await response.json();
    
    // Cache for 6 months (default duration)
    await cache.set(cacheKey, locations);

    return locations.find(loc => loc.slug === slug || loc.Places.toLowerCase().replace(/\s+/g, '-') === slug) || null;
  } catch (error) {
    console.error('Error fetching location:', error);
    return null;
  }
}

async function getServices(): Promise<Keyword[]> {
  try {
    const baseUrl = process.env.NODE_ENV === 'production' 
      ? process.env.NEXT_PUBLIC_PRODUCTION_URL || 'https://cleaningservicesmp.info'
      : process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    const cache = new Cache({
      debug: true
    });
    const cacheKey = 'services:allKeywords';
    
    // Check cache first
    const cachedServices = await cache.get<Keyword[]>(cacheKey);
    if (cachedServices) {
      return cachedServices;
    }

    // Fetch if not in cache
    const response = await fetch(`${baseUrl}/api/keywords`);

    if (!response.ok) {
      throw new Error('Failed to fetch services');
    }

    const services = await response.json();
    
    // Cache for 6 months (default duration)
    await cache.set(cacheKey, services);

    return services;
  } catch (error) {
    console.error('Error fetching services:', error);
    return [];
  }
}

export async function generateMetadata(
  { params }: ServicesPageProps
): Promise<Metadata> {
  const { location } = await params;
  const locationData = await getLocationData(location);
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://cleaningservicesmp.info';
  
  return {
    title: `Cleaning Services in ${locationData?.Places || 'Madhya Pradesh'}`,
    description: `Find the best cleaning services in ${locationData?.Places || 'Madhya Pradesh'}. Compare top-rated cleaning services.`,
    alternates: {
      canonical: `${baseUrl}/services/${location}`
    }
  };
}

export default async function ServicesPage({ params }: ServicesPageProps): Promise<React.JSX.Element> {
  const { location } = await params;
  const [locationData, services] = await Promise.all([
    getLocationData(location),
    getServices()
  ]);

  if (!locationData) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900">Location not found</h1>
            <p className="mt-4 text-lg text-gray-600">
              The location you&apos;re looking for doesn&apos;t exist.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-gray-900 mb-4">
            Cleaning Services in {locationData.Places}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Find and compare the best cleaning services in {locationData.Places}, {locationData.District}
          </p>
        </div>

        <div className="mb-12">
          <SearchBar />
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {services.map((service) => (
            <a
              key={service.slug || service.keyword}
              href={`/search?q=${encodeURIComponent(`${service.keyword} in ${locationData.Places} Madhya Pradesh`)}`}
              className="block transform transition-all duration-300 hover:scale-105"
            >
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl border border-gray-100">
                <div className="p-8">
                  <h2 className="text-xl font-semibold text-gray-900 mb-4">
                    {service.keyword}
                  </h2>
                  <p className="text-gray-600 mb-4">
                    Professional {service.keyword.toLowerCase()} services in {locationData.Places}
                  </p>
                  <div className="inline-flex items-center text-blue-600 font-semibold group">
                    View Providers
                    <svg 
                      className="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </a>
          ))}
        </div>
      </div>
    </div>
  );
} 