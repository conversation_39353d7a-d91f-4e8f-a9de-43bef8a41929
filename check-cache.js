const { MongoClient } = require('mongodb');

async function checkCache() {
  const uri = 'mongodb+srv://turningspace:<EMAIL>/cleaningservices?retryWrites=true&w=majority&appName=Cluster0';
  const client = new MongoClient(uri);

  try {
    await client.connect();
    console.log('Connected to MongoDB Atlas');

    const db = client.db('cleaningservices');
    const collections = await db.listCollections().toArray();
    console.log('\nCollections in database:');
    collections.forEach(col => console.log(`- ${col.name}`));

    // Check all cache-related collections
    const cacheCollections = ['cache', 'searchResults', 'searchbar:locations', 'searchbar:keywords'];
    for (const collectionName of cacheCollections) {
      console.log(`\n--- Checking ${collectionName} collection ---`);
      const collection = db.collection(collectionName);
      
      try {
        const count = await collection.countDocuments();
        console.log(`Total documents: ${count}`);

        if (count > 0) {
          const results = await collection.find({}).toArray();
          console.log('\nLatest cache entries:');
          results.slice(0, 5).forEach(doc => {
            console.log('\nDocument:');
            console.log('- Key:', doc.key || 'N/A');
            console.log('- Timestamp:', doc.timestamp || 'N/A');
            console.log('- Expires:', doc.expiresAt || 'N/A');
            
            // Optional: Log a snippet of the value
            if (doc.value) {
              console.log('- Value (first item):', 
                Array.isArray(doc.value) ? 
                  JSON.stringify(doc.value[0]) : 
                  JSON.stringify(doc.value).slice(0, 100) + '...'
              );
            }
          });
        }

        // Check indexes
        const indexes = await collection.indexes();
        console.log('\nIndexes:');
        indexes.forEach(index => console.log(`- ${JSON.stringify(index.key)}`));

      } catch (error) {
        console.log(`Collection ${collectionName} not found or not accessible`);
        console.error(error);
      }
    }

  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
}

checkCache(); 