import { NextResponse } from 'next/server';
import { Cache } from '@/app/lib/cache';
import { parse } from 'csv-parse/sync';
import { promises as fs } from 'fs';
import path from 'path';

export const runtime = 'nodejs';

const cache = new Cache({ 
  collection: 'locations',
  debug: true 
});

async function getLocationsFromCSV() {
  try {
    const csvPath = path.join(process.cwd(), 'data', 'locations.csv');
    const fileContents = await fs.readFile(csvPath, 'utf-8');
    
    const records = parse(fileContents, {
      columns: true,
      skip_empty_lines: true
    });

    const locations = records.map((record: any) => ({
      Places: record.Places.trim(),
      District: record.District.trim(),
      Type: record.Type?.trim() || 'City',
      slug: record.Places.toLowerCase().replace(/\s+/g, '-')
    }));

    return locations;
  } catch (error) {
    console.error('Error reading locations from CSV:', error);
    throw error;
  }
}

export async function GET() {
  try {
    // Try to get from cache first
    let locations;
    try {
      const cachedLocations = await cache.get<any[]>('locations:all');
      if (cachedLocations) {
        console.log('[Locations] Cache hit');
        locations = cachedLocations;
      }
    } catch (cacheError) {
      console.error('[Locations] Cache error:', cacheError);
      // Continue to CSV fallback
    }

    // If not in cache or cache failed, read from CSV
    if (!locations) {
      console.log('[Locations] Reading from CSV');
      locations = await getLocationsFromCSV();

      // Try to cache the results, but don't block on failure
      try {
        await cache.set('locations:all', locations);
      } catch (cacheError) {
        console.error('[Locations] Failed to cache results:', cacheError);
      }
    }

    return NextResponse.json(locations, {
      headers: {
        'Cache-Control': 'public, max-age=15552000, immutable',
        'X-Cache': locations ? 'HIT' : 'MISS'
      }
    });
  } catch (error) {
    console.error('Error fetching locations:', error);
    return NextResponse.json([], {
      status: 500,
      headers: {
        'Cache-Control': 'public, max-age=15552000, immutable',
        'X-Cache': 'ERROR'
      }
    });
  }
} 