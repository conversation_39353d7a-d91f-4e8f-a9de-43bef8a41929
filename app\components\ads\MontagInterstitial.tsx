'use client';

export function MontagInterstitial() {
  const network = process.env.NEXT_PUBLIC_AD_NETWORK;
  
  // Show Monetag interstitial if network is 'monetag' OR 'both'
  if (network === 'monetag' || network === 'both') {
    return (
      <script
        dangerouslySetInnerHTML={{
          __html: `
            (function(s){
              s.dataset.zone='9891149',
              s.src='https://groleegni.net/vignette.min.js'
            })([document.documentElement, document.body].filter(Boolean).pop().appendChild(document.createElement('script')))
          `
        }}
      />
    );
  }
  
  return null;
}
