const fs = require('fs');
const path = require('path');
const { parse } = require('csv-parse/sync');
const { MongoClient } = require('mongodb');

// Skip sitemap generation during builds unless forced
const SKIP_SITEMAP_GENERATION = process.env.SKIP_SITEMAP_GENERATION === 'true';
const FORCE_SITEMAP_GENERATION = process.env.FORCE_SITEMAP_GENERATION === 'true';
const baseUrlWWW = 'https://www.cleaningservicesmp.info';
const baseUrlNonWWW = 'https://cleaningservicesmp.info';

// Static pages configuration
const staticPages = [
  { url: '', priority: '1.0', changefreq: 'daily' },
  { url: '/services', priority: '0.9', changefreq: 'daily' },
  { url: '/about', priority: '0.7', changefreq: 'monthly' },
  { url: '/contact', priority: '0.8', changefreq: 'monthly' },
  { url: '/privacy-policy', priority: '0.3', changefreq: 'yearly' },
  { url: '/terms-of-service', priority: '0.3', changefreq: 'yearly' },
  { url: '/blog', priority: '0.8', changefreq: 'weekly' },
  { url: '/blog/kitchen-cleaning-tips-madhya-pradesh-weather', priority: '0.7', changefreq: 'monthly' }
];

function getDataFromCSV() {
  try {
    // Read locations
    const locationsPath = path.join(process.cwd(), 'data', 'locations.csv');
    const locationsContent = fs.readFileSync(locationsPath, 'utf-8');
    const locationsRecords = parse(locationsContent, {
      columns: true,
      skip_empty_lines: true
    });
    const locations = locationsRecords.map(record => ({
      Places: record.Places.trim(),
      slug: record.Places.toLowerCase().replace(/\s+/g, '-')
    }));

    // Read keywords/services
    const keywordsPath = path.join(process.cwd(), 'data', 'keywords.csv');
    const keywordsContent = fs.readFileSync(keywordsPath, 'utf-8');
    const keywordsRecords = parse(keywordsContent, {
      columns: true,
      skip_empty_lines: true
    });
    const keywords = keywordsRecords.map(record => ({
      keyword: record.keyword.trim(),
      slug: record.keyword.toLowerCase().replace(/\s+/g, '-')
    }));

    return { locations, keywords };
  } catch (error) {
    console.error('Error reading CSV data:', error);
    throw error;
  }
}

function generateSitemapXML(urls) {
  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls.map(url => `  <url>
    <loc>${url.url}</loc>
    <lastmod>${url.lastmod}</lastmod>
    <changefreq>${url.changefreq}</changefreq>
    <priority>${url.priority}</priority>
  </url>`).join('\n')}
</urlset>`;
}

function generateSitemapIndex(sitemaps) {
  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemaps.map(sitemap => `  <sitemap>
    <loc>${sitemap}</loc>
  </sitemap>`).join('\n')}
</sitemapindex>`;
}

async function generateSitemaps() {
  if (SKIP_SITEMAP_GENERATION && !FORCE_SITEMAP_GENERATION) {
    console.log('Skipping sitemap generation completely due to SKIP_SITEMAP_GENERATION=true');
    return;
  }

  const currentDate = new Date().toISOString();
  
  try {
    // Generate static sitemap (sitemap-0.xml)
    const staticUrls = [
      // WWW version of static pages
      ...staticPages.map(page => ({
        url: `${baseUrlWWW}${page.url}`,
        lastmod: currentDate,
        changefreq: page.changefreq,
        priority: page.priority
      })),
      // Non-WWW version of static pages
      ...staticPages.map(page => ({
        url: `${baseUrlNonWWW}${page.url}`,
        lastmod: currentDate,
        changefreq: page.changefreq,
        priority: page.priority
      }))
    ];
    
    const staticSitemap = generateSitemapXML(staticUrls);
    fs.writeFileSync(path.join(process.cwd(), 'public', 'sitemap-0.xml'), '\ufeff' + staticSitemap, 'utf8');
    console.log('Generated sitemap-0.xml');

    // Generate dynamic sitemap (server-sitemap.xml)
    const { locations, keywords } = getDataFromCSV();
    
    const dynamicUrls = [
      // WWW version
      ...locations.flatMap(location =>
        keywords.map(service => ({
          url: `${baseUrlWWW}/search?q=${encodeURIComponent(`${service.keyword} in ${location.Places} Madhya Pradesh`)}`,
          lastmod: currentDate,
          changefreq: 'daily',
          priority: '0.7'
        }))
      ),
      // Non-WWW version
      ...locations.flatMap(location =>
        keywords.map(service => ({
          url: `${baseUrlNonWWW}/search?q=${encodeURIComponent(`${service.keyword} in ${location.Places} Madhya Pradesh`)}`,
          lastmod: currentDate,
          changefreq: 'daily',
          priority: '0.7'
        }))
      )
    ];

    const serverSitemap = generateSitemapXML(dynamicUrls);
    fs.writeFileSync(path.join(process.cwd(), 'public', 'server-sitemap.xml'), '\ufeff' + serverSitemap, 'utf8');
    console.log('Generated server-sitemap.xml');

    // Generate sitemap index (sitemap.xml)
    const sitemapIndex = generateSitemapIndex([
      `${baseUrlWWW}/sitemap-0.xml`,
      `${baseUrlWWW}/server-sitemap.xml`
    ]);
    fs.writeFileSync(path.join(process.cwd(), 'public', 'sitemap.xml'), '\ufeff' + sitemapIndex, 'utf8');
    console.log('Generated sitemap.xml index');

    // Generate robots.txt
    const robotsTxt = `User-agent: *
Allow: /

Sitemap: ${baseUrlWWW}/sitemap.xml`;
    fs.writeFileSync(path.join(process.cwd(), 'public', 'robots.txt'), robotsTxt, 'utf8');
    console.log('Generated robots.txt');

  } catch (error) {
    console.error('Error generating sitemaps:', error);
    process.exit(1);
  }
}

generateSitemaps(); 