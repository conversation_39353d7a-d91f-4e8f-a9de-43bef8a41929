export interface Location {
  place: string;
  district: string;
  type: string;
}

export interface Keyword {
  keyword: string;
}

export interface SearchResult {
  name: string;
  address: string;
  rating?: number;
  types: string[];
  place_id: string;
}

export interface ContactForm {
  name: string;
  email: string;
  phone: string;
  message: string;
  service: string;
  location: string;
  timestamp: Date;
}

export interface MetaData {
  title: string;
  description: string;
  keywords: string[];
} 