import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Privacy Policy - Cleaning Services MP',
  description: 'Privacy policy for Cleaning Services MP cleaning service platform.',
};

export default function PrivacyPolicyPage(): React.JSX.Element {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-4xl font-extrabold text-gray-900 mb-8">Privacy Policy</h1>
        
        <section className="mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">1. Information We Collect</h2>
          <p className="text-gray-600">
            cleaningservicesmp.info collects personal information that you provide directly, 
            such as name, contact details, and service preferences.
          </p>
        </section>

        <section className="mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">2. How We Use Your Information</h2>
          <ul className="list-disc list-inside text-gray-600 space-y-2">
            <li>To provide and manage cleaning services</li>
            <li>To communicate with you about bookings</li>
            <li>To improve our platform</li>
          </ul>
        </section>

        <section className="mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">3. Data Protection</h2>
          <p className="text-gray-600">
            cleaningservicesmp.info implements industry-standard security measures to protect 
            your personal information from unauthorized access.
          </p>
        </section>

        <section className="mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">4. Third-Party Services</h2>
          <p className="text-gray-600">
            We may share your information with cleaning service providers to facilitate bookings. 
            These providers are obligated to protect your data.
          </p>
        </section>

        <section className="mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">5. Your Rights</h2>
          <ul className="list-disc list-inside text-gray-600 space-y-2">
            <li>Access your personal information</li>
            <li>Request correction of your data</li>
            <li>Request deletion of your information</li>
          </ul>
        </section>

        <section>
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">6. Contact</h2>
          <p className="text-gray-600">
            For privacy concerns or data requests, contact us through the contact form on cleaningservicesmp.info.
          </p>
        </section>
      </div>
    </div>
  );
} 