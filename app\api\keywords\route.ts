import { NextResponse } from 'next/server';
import { Cache } from '@/app/lib/cache';
import { parse } from 'csv-parse/sync';
import { promises as fs } from 'fs';
import path from 'path';

export const runtime = 'nodejs';

const cache = new Cache({ 
  collection: 'keywords',
  debug: true 
});

async function getKeywordsFromCSV() {
  try {
    const csvPath = path.join(process.cwd(), 'data', 'keywords.csv');
    const fileContents = await fs.readFile(csvPath, 'utf-8');
    
    const records = parse(fileContents, {
      columns: true,
      skip_empty_lines: true
    });

    const keywords = records.map((record: any) => ({
      keyword: record.keyword.trim(),
      slug: record.keyword.toLowerCase().replace(/\s+/g, '-')
    }));

    return keywords;
  } catch (error) {
    console.error('Error reading keywords from CSV:', error);
    throw error;
  }
}

export async function GET() {
  try {
    // Try to get from cache first
    let keywords;
    try {
      const cachedKeywords = await cache.get<any[]>('keywords:all');
      if (cachedKeywords) {
        console.log('[Keywords] Cache hit');
        keywords = cachedKeywords;
      }
    } catch (cacheError) {
      console.error('[Keywords] Cache error:', cacheError);
      // Continue to CSV fallback
    }

    // If not in cache or cache failed, read from CSV
    if (!keywords) {
      console.log('[Keywords] Reading from CSV');
      keywords = await getKeywordsFromCSV();

      // Try to cache the results, but don't block on failure
      try {
        await cache.set('keywords:all', keywords);
      } catch (cacheError) {
        console.error('[Keywords] Failed to cache results:', cacheError);
      }
    }

    return NextResponse.json(keywords, {
      headers: {
        'Cache-Control': 'public, max-age=15552000, immutable',
        'X-Cache': keywords ? 'HIT' : 'MISS'
      }
    });
  } catch (error) {
    console.error('Error fetching keywords:', error);
    return NextResponse.json([], {
      status: 500,
      headers: {
        'Cache-Control': 'public, max-age=15552000, immutable',
        'X-Cache': 'ERROR'
      }
    });
  }
} 