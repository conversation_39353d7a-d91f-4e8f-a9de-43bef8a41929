import { MongoClient, MongoClientOptions } from 'mongodb';

// Ensure the MongoDB URI is configured
const uri = process.env.MONGODB_URI;
if (!uri) {
  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');
}

// Assert uri as string since we've checked it's not undefined
const mongoUri: string = uri;

// Minimal connection options
const options: MongoClientOptions = {
  maxPoolSize: 5,
  minPoolSize: 1,
  serverSelectionTimeoutMS: 30000,
  socketTimeoutMS: 45000,
  connectTimeoutMS: 10000,
  retryWrites: true,
  w: 'majority' as const
};

let client: MongoClient | null = null;

export async function getMongoClient() {
  if (!client) {
    client = new MongoClient(mongoUri, options);
    await client.connect();
    console.log('MongoDB connection initialized');
  }
  return client;
}

export async function getMongoDb(dbName: string = 'cleaningservices') {
  const client = await getMongoClient();
  return client.db(dbName);
}

export default {
  getMongoClient,
  getMongoDb
}; 