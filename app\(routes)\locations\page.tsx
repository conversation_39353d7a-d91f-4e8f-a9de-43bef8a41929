import React from 'react';
import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import { Location } from '@/app/types';
import { Cache } from '@/app/lib/cache';

interface PageProps {
  params: Promise<Record<string, string>>;
  searchParams: Promise<Record<string, string>>;
}

export const metadata: Metadata = {
  title: 'Cleaning Service Locations in Madhya Pradesh',
  description: 'Find cleaning services in all major cities of Madhya Pradesh. Professional cleaning services available across MP.',
};

async function getLocations(): Promise<Location[]> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 
                    process.env.VERCEL_URL || 
                    'http://localhost:3000';

    const cache = new Cache({
      debug: true
    });
    const cacheKey = 'locations:allLocations';
    
    // Check cache first
    const cachedLocations = await cache.get<Location[]>(cacheKey);
    if (cachedLocations) {
      return cachedLocations;
    }

    // Fetch if not in cache
    const response = await fetch(`${baseUrl}/api/locations`);

    if (!response.ok) {
      console.error('Failed to fetch locations:', response.status);
      return [];
    }

    const locations = await response.json();
    
    // Cache for 6 months (default duration)
    await cache.set(cacheKey, locations);

    return locations;
  } catch (error) {
    console.error('Error fetching locations:', error);
    return [];
  }
}

export default async function LocationsPage({
  searchParams,
}: PageProps): Promise<React.JSX.Element> {
  const locations = await getLocations();
  const { service: selectedService } = await searchParams;

  if (!locations.length) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-gray-900">No locations found</h1>
            <p className="mt-4 text-lg text-gray-600">
              Please try again later.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-extrabold text-gray-900 mb-4">
            {selectedService ? `${selectedService} Locations` : 'Our Service Locations'}
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            {selectedService 
              ? `Find professional ${selectedService.toLowerCase()} services in major cities across Madhya Pradesh`
              : 'Find professional cleaning services in major cities across Madhya Pradesh'
            }
          </p>
        </div>

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
          {locations.map((location) => (
            <Link
              key={location.slug}
              href={selectedService 
                ? `/search?q=${encodeURIComponent(`${selectedService} in ${location.Places} Madhya Pradesh`)}`
                : `/services/${location.slug}`
              }
              className="block transform transition-all duration-300 hover:scale-105"
            >
              <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl border border-gray-100">
                <div className="p-8">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-2xl font-bold text-gray-900">
                      {location.Places}
                    </h2>
                    <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                      {location.Type}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-4">
                    {location.District}
                  </p>
                  <div className="inline-flex items-center text-blue-600 font-semibold group">
                    {selectedService ? 'View Results' : 'View Services'}
                    <svg 
                      className="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" 
                      fill="none" 
                      stroke="currentColor" 
                      viewBox="0 0 24 24"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
} 