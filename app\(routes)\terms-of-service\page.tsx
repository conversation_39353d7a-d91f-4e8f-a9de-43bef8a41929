import React from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Terms of Service - Cleaning Services MP',
  description: 'Terms of service for Cleaning Services MP cleaning service platform.',
};

export default function TermsOfServicePage(): React.JSX.Element {
  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-50 to-white py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-4xl font-extrabold text-gray-900 mb-8">Terms of Service</h1>
        
        <section className="mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">1. Acceptance of Terms</h2>
          <p className="text-gray-600">
            By accessing and using the cleaningservicesmp.info platform, you agree to these Terms of Service. 
            If you do not agree, please do not use our services.
          </p>
        </section>

        <section className="mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">2. Service Description</h2>
          <p className="text-gray-600">
            cleaningservicesmp.info is a platform connecting cleaning service providers with customers 
            in Madhya Pradesh. We facilitate service discovery and booking.
          </p>
        </section>

        <section className="mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">3. User Responsibilities</h2>
          <ul className="list-disc list-inside text-gray-600 space-y-2">
            <li>Provide accurate and current information</li>
            <li>Respect service providers and their terms</li>
            <li>Use the platform for legitimate purposes</li>
          </ul>
        </section>

        <section className="mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">4. Limitation of Liability</h2>
          <p className="text-gray-600">
            cleaningservicesmp.info is not responsible for the quality of services provided by 
            third-party cleaning service providers. Users engage with providers at their own risk.
          </p>
        </section>

        <section className="mb-6">
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">5. Privacy</h2>
          <p className="text-gray-600">
            Your use of cleaningservicesmp.info is also governed by our Privacy Policy. 
            Please review it to understand our data practices.
          </p>
        </section>

        <section>
          <h2 className="text-2xl font-semibold text-gray-800 mb-4">6. Contact</h2>
          <p className="text-gray-600">
            For any questions about these Terms, contact us through the contact form on cleaningservicesmp.info.
          </p>
        </section>
      </div>
    </div>
  );
} 