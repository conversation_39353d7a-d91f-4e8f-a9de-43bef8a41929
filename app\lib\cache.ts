import { Collection, Db, Filter, Document, FindOneAndUpdateOptions, ReturnDocument } from 'mongodb';
import { getMongoDb } from './mongodb';

interface CacheDocument extends Document {
  _id: string;
  key: string;
  value: any;
  hits: number;
  timestamp: Date;
  expiresAt: Date;
  lastAccessed: Date;
  createdAt: Date;
}

interface CacheOptions {
  collection?: string;
  debug?: boolean;
  ttl?: number;
}

export class Cache {
  private collection: Collection<CacheDocument> | null = null;
  private options: Required<CacheOptions>;
  private db: Db | null = null;

  constructor(options: CacheOptions = {}) {
    this.options = {
      collection: options.collection || 'cache',
      debug: options.debug || false,
      ttl: options.ttl || 15552000 // 6 months default
    };

    // Initialize immediately in constructor
    this.initialize().catch(error => {
      console.error('[Cache] Initialization failed:', error);
    });
  }

  private log(message: string, ...args: any[]) {
    if (this.options.debug) {
      console.log(`[Cache:${this.options.collection}] ${message}`, ...args);
    }
  }

  private async initialize() {
    try {
      if (!this.db || !this.collection) {
        this.db = await getMongoDb();
        this.collection = this.db.collection<CacheDocument>(this.options.collection);
        this.log('Collection initialized');
      }
    } catch (error) {
      console.error(`[Cache:${this.options.collection}] Initialization error:`, error);
      throw error;
    }
  }

  public async ensureInitialized() {
    if (!this.collection) {
      await this.initialize();
      if (!this.collection) {
        throw new Error('Failed to initialize cache collection');
      }
    }
  }

  async get<T>(key: string): Promise<T | null> {
    try {
      await this.ensureInitialized();
      if (!this.collection) throw new Error('Cache not initialized');

      const filter: Filter<CacheDocument> = { _id: key };
      const update = {
        $inc: { hits: 1 },
        $set: { lastAccessed: new Date() }
      };
      const options: FindOneAndUpdateOptions = { 
        returnDocument: 'after' as ReturnDocument
      };

      const doc = await this.collection.findOneAndUpdate(filter, update, options);
      const hit = doc !== null;
      
      this.log(`Get key: ${key}${hit ? ', HIT' : ', MISS'}`);
      if (hit) {
        this.log(`Cache hit details - Hits: ${doc.hits}, Last accessed: ${doc.lastAccessed}`);
      }

      return doc?.value ? (doc.value as T) : null;
    } catch (error) {
      console.error(`[Cache:${this.options.collection}] Get error for key ${key}:`, error);
      throw error;
    }
  }

  async set(key: string, value: any): Promise<void> {
    try {
      await this.ensureInitialized();
      if (!this.collection) throw new Error('Cache not initialized');

      const now = new Date();
      const expiresAt = new Date(now.getTime() + (this.options.ttl * 1000));

      const filter: Filter<CacheDocument> = { _id: key };
      const update = {
        $set: {
          key,
          value,
          timestamp: now,
          expiresAt,
          lastAccessed: now,
          createdAt: now
        },
        $setOnInsert: {
          hits: 0
        }
      };

      await this.collection.updateOne(filter, update, { upsert: true });
      this.log(`Set key: ${key}`);
    } catch (error) {
      console.error(`[Cache] Set error for key ${key}:`, error);
      throw error;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await this.ensureInitialized();
      if (!this.collection) throw new Error('Cache not initialized');

      const filter: Filter<CacheDocument> = { _id: key };
      await this.collection.deleteOne(filter);
      this.log(`Delete key: ${key}`);
    } catch (error) {
      console.error(`[Cache] Delete error for key ${key}:`, error);
      throw error;
    }
  }
} 