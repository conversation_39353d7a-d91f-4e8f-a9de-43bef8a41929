'use client';

export function NativeBanner() {
  const network = process.env.NEXT_PUBLIC_AD_NETWORK;

  // Show Adsterra native banner if network is 'adsterra' OR 'both'
  if (network === 'adsterra' || network === 'both') {
    return (
      <>
        <script
          async
          data-cfasync="false"
          src={process.env.NEXT_PUBLIC_ADSTERRA_NATIVE_SCRIPT}
        />
        <div id={process.env.NEXT_PUBLIC_ADSTERRA_NATIVE_CONTAINER}></div>
      </>
    );
  }

  return null;
}
