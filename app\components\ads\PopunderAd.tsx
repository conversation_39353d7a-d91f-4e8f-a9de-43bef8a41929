'use client';

export function PopunderAd() {
  const network = process.env.NEXT_PUBLIC_AD_NETWORK;

  // Show Monetag popunder if network is 'monetag' OR 'both'
  if (network === 'monetag' || network === 'both') {
    return (
      <script
        dangerouslySetInnerHTML={{
          __html: `
            (s=>{
              s.dataset.zone='${process.env.NEXT_PUBLIC_MONETAG_POPUNDER_ZONE}',
              s.src='https://al5sm.com/tag.min.js'
            })([document.documentElement, document.body].filter(Boolean).pop().appendChild(document.createElement('script')))
          `
        }}
      />
    );
  }

  return null;
}
