export interface Location {
  Places: string;
  District: string;
  Type: string;
  slug?: string;
}

export interface Keyword {
  keyword: string;
  slug?: string;
}

export interface SearchResult {
  name: string;
  address: string;
  rating: number;
  types: string[];
  place_id: string;
  phone?: string;
  website?: string;
  googleMapsUrl?: string;
}

export interface ContactForm {
  name: string;
  email: string;
  phone: string;
  message: string;
  service: string;
  location: string;
  timestamp: Date;
}

export interface MetaData {
  title: string;
  description: string;
  keywords: string[];
} 